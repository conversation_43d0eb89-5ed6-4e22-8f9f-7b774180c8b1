---
import type { Props } from '@astrojs/starlight/props'
import Default from '@astrojs/starlight/components/Head.astro'
import { BANNER_PATH, DEFAULT_BANNER_IMAGE, getBannerImageFilename, checkBannerImageExists } from './imagePaths'

const bannerImageFileName = getBannerImageFilename({ path: Astro.props.id })
const imageExists = checkBannerImageExists({ bannerImageFileName })

// Get the URL of the social media preview image for the current post using its
// slug ('Astro.props.id') and replace the path and file extension with `.webp`.
let ogImageUrl = new URL(
  `${BANNER_PATH}/${DEFAULT_BANNER_IMAGE}`,
  Astro.site,
);
if (imageExists) {
  ogImageUrl = new URL(
    `${BANNER_PATH}/${bannerImageFileName}`,
    Astro.site,
  )
}
---

<Default {...Astro.props}><slot /></Default>

<!-- Open Graph images. -->
<meta property="og:image" content={ogImageUrl} />
<meta property="og:image:width" content="1200" />
<meta property="og:image:height" content="630" />
<meta name="twitter:image" content={ogImageUrl} />