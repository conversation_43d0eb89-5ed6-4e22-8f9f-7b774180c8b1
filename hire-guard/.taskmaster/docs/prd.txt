# HireGuard AI Development Plan: Iterative Vertical Slice (MVP-First Approach)

This document outlines a step-by-step development plan for building the HireGuard AI application, starting with an MVP and progressing iteratively. The plan leverages the existing `hire-guard` boilerplate, focusing on extending its functionalities.

**Plan Style: Iterative Vertical Slice (MVP-First Approach)**

**Reasoning for this Plan Style:**
This approach allows us to immediately utilize the pre-existing modules in `hire-guard` (like file upload, admin dashboard, and authentication) and build the new AI features on top of them, rather than re-creating core functionalities. By breaking down the development into small, self-contained, end-to-end "slices" (e.g., from UI input to basic AI processing and display), each step becomes a manageable and actionable task for an LLM. This promotes efficient iterative development and allows for quick validation of individual components. We can deliver a functional Minimum Viable Product (MVP) that demonstrates the core value proposition quickly, allowing for early feedback and validation.

---

## Phase 0: Initial Project Refactoring (Folder Renames)

To align the project structure more closely with the HireGuard AI concept, we will rename two key directories. This will improve clarity and maintainability as we integrate the new AI features.

*   **Actionable Steps:**
    *   **0.1. Rename `app/src/demo-ai-app/` to `app/src/risk-engine/`**
        *   **Reasoning:** This folder will house the core AI logic for risk analysis, anomaly detection, and scoring, making `risk-engine` a more descriptive name.
    *   **0.2. Rename `app/src/file-upload/` to `app/src/data-ingestion/`**
        *   **Reasoning:** This folder will handle all forms of candidate data input, including resume uploads, LinkedIn profile parsing, and future integrations (e.g., GitHub, Calendar). `data-ingestion` is a broader and more accurate term than `file-upload`.

---

## Phase 1: Minimum Viable Product (MVP)

The MVP for HireGuard AI is designed to deliver immediate, tangible value to startups by addressing critical early-stage hiring challenges. It focuses on providing rapid insights to mitigate hiring risks, streamline vetting processes, and help allocate scarce resources effectively. This phase will establish the core data pipeline and a basic risk detection mechanism.

### 1. Data Model Foundation in Prisma & Wasp

**Goal:** Establish a flexible and scalable data model to store candidate information, employment history, GitHub activities, and initial risk reports. This foundation is crucial for startups as it allows for rapid iteration and future expansion without major architectural overhauls.

*   **Actionable Steps:**
    *   **1.1. Update `app/schema.prisma`:**
        *   Define `Candidate` entity (basic info, user relation, `id` should be `UUID` or `CUID`, `createdAt`, `updatedAt`).
        *   Define `EmploymentRecord` entity (company, title, `startDate`, `endDate` (optional), `source` (e.g., 'resume', 'linkedin'), relation to `Candidate`).
        *   Define `GitHubActivity` entity (repository URL, `commitTimestamp`, `commitMessage`, `source` ('github'), relation to `Candidate`).
        *   Define `RiskReport` entity (`candidateId`, `riskScore` (e.g., `Float` for 0-100), `overlapDetected: Boolean`, `overlapDetails: String[]`, `generationDate`, `status` (e.g., 'pending', 'completed')).
        *   Define necessary relations (e.g., one-to-many from `Candidate` to `EmploymentRecord`, `GitHubActivity`, `RiskReport`).
    *   **1.2. Update `app/main.wasp`:**
        *   Declare new Wasp `entity` definitions corresponding to the Prisma models (`Candidate`, `EmploymentRecord`, `GitHubActivity`, `RiskReport`).
        *   Define initial Wasp `query` operations to fetch candidates and their risk reports (`getCandidates: [Candidate]`, `getRiskReport: RiskReport`).
        *   Define initial Wasp `action` operations to create/update candidates (`createCandidate: Candidate -> Candidate`, `updateCandidate: Candidate -> Candidate`).

### 2. Candidate Input & Data Ingestion (Resume + LinkedIn)

**Goal:** Enable startups to quickly ingest candidate data from common sources (resumes, LinkedIn profiles) to reduce manual data entry and accelerate the initial screening process. This provides immediate efficiency gains.

*   **Actionable Steps:**
    *   **2.1. Extend `app/src/user/AccountPage.tsx` or create `app/src/client/components/CandidateInputForm.tsx`:**
        *   Add a dedicated section for "Candidate Data Input" in the user account page or a new designated input page.
        *   Include UI elements for resume file upload (leveraging and extending `app/src/data-ingestion/FileUploadPage.tsx` components and logic for `File` type processing).
        *   Add a multi-line text area or input field for pasting LinkedIn profile URLs or raw text.
        *   Implement robust client-side validation for input types and formats (e.g., file size, URL format).
    *   **2.2. Enhance `app/src/data-ingestion/fileUploading.ts` and `app/src/data-ingestion/operations.ts`:**
        *   Modify existing `uploadFile` (if applicable) or create a new `uploadResume` Wasp `action` to handle resume file storage (via S3 integration already present) and trigger backend parsing.
        *   Create a new Wasp `action` (`processLinkedInProfile: { url: string } -> Candidate`) for sending LinkedIn profile data to the backend.
    *   **2.3. Implement Initial Resume Parsing & LinkedIn Scraping (Backend):**
        *   In `app/src/server/data-ingestion/resumeParser.ts` (new file) or extend `app/src/server/utils.ts`, implement a basic, rule-based resume parser to extract name, email, and `EmploymentRecord` details. Prioritize key-value extraction for MVP.
        *   In `app/src/server/data-ingestion/linkedInProcessor.ts` (new file), implement a basic function to process a *pasted* LinkedIn profile URL or text. For MVP, this might involve simple text parsing to extract job titles and dates. *(Self-correction: Full real-time LinkedIn scraping is complex and often violates terms of service. For MVP, focus on processing user-provided textual data or integrating with an existing LinkedIn API service if available, otherwise, emphasize the placeholder nature for later robust integration).*
        *   Ensure both parsing functions call Wasp `action`s to save the extracted `Candidate` and `EmploymentRecord` data to the database.

### 3. GitHub Commit Analysis

**Goal:** Gain early insight into a candidate's professional activity and potential red flags by analyzing their public code contributions. This helps startups assess commitment and identify immediate conflicts of interest.

*   **Actionable Steps:**
    *   **3.1. Extend `app/src/user/AccountPage.tsx` (or `CandidateInputForm.tsx`):**
        *   Add a UI element (input field or button) for candidates to provide their GitHub username or public repository URLs.
        *   Consider a clear consent message regarding public data access.
        *   Implement client-side calls to trigger the backend GitHub analysis.
    *   **3.2. Create `app/src/risk-engine/operations.ts` (if not already for this purpose) and add server-side logic:**
        *   Define a new Wasp `action` (`analyzeGitHubActivity: { username: string, repos: string[] } -> void`) that takes GitHub details and triggers backend processing.
        *   In `app/src/server/utils.ts` or `app/src/risk-engine/githubAnalyzer.ts` (new file), implement a function to call the GitHub API (e.g., using a lightweight Node.js HTTP client or a dedicated library if the backend is Python) to fetch public commit timestamps and messages for the specified user/repositories.
        *   Store relevant `GitHubActivity` (commit `timestamp`, `message`, `repoName`) in the database via a Wasp `action` (`createGitHubActivity: GitHubActivity -> GitHubActivity`).

### 4. Basic Risk Analysis Engine (Timeline Conflict Detection)

**Goal:** Provide quick, automated detection of the most critical red flag for startups: overlapping employment. This helps avoid costly mis-hires and protects sensitive IP.

*   **Actionable Steps:**
    *   **4.1. Develop `app/src/risk-engine/schedule.ts` (for background processing) or `app/src/risk-engine/operations.ts` (for on-demand processing):**
        *   Create a Wasp `task` (e.g., `analyzeCandidateRisk: { candidateId: ID } -> RiskReport`) that can be triggered manually by a recruiter or as a background job after data ingestion.
        *   This task will:
            *   Fetch all `EmploymentRecord`s for a given `candidateId`.
            *   Fetch all `GitHubActivity` for the same `candidateId`.
            *   Implement timeline overlap detection logic:
                *   Compare all `EmploymentRecord` date ranges against each other.
                *   Compare `GitHubActivity` timestamps against declared `EmploymentRecord` periods (e.g., flag commits outside reported employment, or during stated leave).
            *   Populate `overlapDetected: Boolean` and `overlapDetails: String[]` (e.g., "Overlap: Company A (Jul 2024-Oct 2024) and Company B (Aug 2024-Dec 2024)").
        *   For MVP, the logic should be straightforward date comparisons.
    *   **4.2. Update `RiskReport` entity in `app/schema.prisma` and `app/main.wasp`:**
        *   Ensure the `RiskReport` entity accurately captures the `overlapDetected` boolean and an array of `overlapDetails` strings.
        *   Define Wasp `action` for `createRiskReport: RiskReport -> RiskReport` and `updateRiskReport: RiskReport -> RiskReport`.
    *   **4.3. Integrate Risk Analysis Trigger:**
        *   Modify the relevant `data-ingestion` Wasp `action`s (e.g., after resume parsing or GitHub analysis) to trigger the `analyzeCandidateRisk` Wasp `task` asynchronously.

### 5. Risk Scoring & Basic Recruiter Dashboard

**Goal:** Deliver immediate, actionable insights to recruiters in a concise format, allowing them to quickly identify high-risk candidates without deep manual investigation. This accelerates the hiring pipeline for fast-growing startups.

*   **Actionable Steps:**
    *   **5.1. Update `app/src/admin/dashboards/users/UsersDashboardPage.tsx` and `app/src/admin/dashboards/users/UsersTable.tsx`:**
        *   Modify the `UsersTable.tsx` (the component displaying the list of users/candidates) to include a new column titled "Risk Score".
        *   Display a simple, color-coded badge or text (e.g., Green/Yellow/Red, or "Low/Medium/High") for the calculated risk score based on the `RiskReport`.
        *   Add an interactive element (e.g., a button or a clickable row) that opens a modal or navigates to a detailed "Risk Report" page for a specific candidate.
    *   **5.2. Create `app/src/admin/components/CandidateRiskReportModal.tsx` (new component):**
        *   Design a simple, clear UI for a modal or a dedicated page that displays:
            *   Candidate's name and basic info.
            *   Overall `riskScore`.
            *   A list of `detectedFlags` and `overlapDetails` (e.g., "Employment Overlap: Company A & B from X to Y").
            *   Implement a Wasp `query` (`getRiskReportForCandidate: { candidateId: ID } -> RiskReport | null`) in `app/src/risk-engine/operations.ts` to fetch the relevant `RiskReport` data for display.
    *   **5.3. Implement Basic Risk Scoring Logic:**
        *   Within `app/src/risk-engine/riskScoring.ts` (new file) or extend `app/src/risk-engine/schedule.ts`, define a basic, deterministic algorithm to calculate the `risk_score`.
        *   For MVP, this could be as simple as: `if (overlapDetected) { score = 80; } else { score = 20; }` or a slightly more nuanced scale based on the number/severity of detected flags.
        *   Ensure this `risk_score` is computed and saved to the `RiskReport` entity when the risk analysis is run.
    *   **5.4. Integrate Initial E2E Tests for MVP:**
        *   In `e2e-tests/tests/`, create a new file like `riskEngineMVP.spec.ts`.
        *   Write basic Playwright tests to:
            *   Verify a candidate can upload a resume/input LinkedIn data.
            *   Verify a candidate can input GitHub details.
            *   Verify the recruiter dashboard loads and displays a "Risk Score" column.
            *   Verify the "Risk Report" modal/page can be opened for a candidate.

---

## Phase 2: Roadmap (6-12 Months) - Iterative Enhancements

These phases will build upon the MVP, adding complexity and expanding data sources and AI capabilities. Each item here represents a new vertical slice, enhancing the value proposition for growing startups.

### 1. Enhanced Data Ingestion & Integrations

**Goal:** Broaden data capture capabilities beyond initial MVP sources to provide a more comprehensive candidate profile, further reducing manual effort for recruiters and enabling deeper insights.

*   **Actionable Steps:**
    *   **1.1. Calendar/Email Integrations:** Implement OAuth flows and API integrations with services like Google Calendar and Outlook in `app/src/data-ingestion/operations.ts` and new backend modules (e.g., `app/src/server/integrations/calendar.ts`). Develop logic for extracting meeting times and potential schedule conflicts.
    *   **1.2. Robust Resume/Document Parsing:** Develop or integrate with more advanced NLP-based resume parsing services/models within `app/src/risk-engine/` (e.g., using a Python backend service if suitable for NLP, orchestrated by Wasp `tasks`) to extract more nuanced information, skills, and qualifications.
    *   **1.3. ATS Integrations:** Explore creating new Wasp `operations` and dedicated server-side connectors (`app/src/server/integrations/ats/`) for popular Applicant Tracking Systems (e.g., Greenhouse, Lever) to seamlessly pull candidate data and push risk reports.
    *   **1.4. Schema Updates:** Update `app/schema.prisma` to store new data types and relations derived from these enhanced integrations (e.g., `CalendarEvent`, `ATSApplication`).

### 2. Deeper Credential Verification

**Goal:** Provide stronger assurances of candidate claims by cross-referencing against authoritative external databases, significantly reducing the risk of fraudulent credentials for startups.

*   **Actionable Steps:**
    *   **2.1. External API Integration:** Implement external API calls to university/degree verification services, professional licensure boards, or industry certification bodies (e.g., in `app/src/risk-engine/credentialVerifier.ts`).
    *   **2.2. NLP for Claim Matching:** Develop more sophisticated NLP models within `app/src/risk-engine/` to intelligently match claimed employment/education details from resumes/LinkedIn against verified external data.
    *   **2.3. Wasp Operations:** Add new Wasp `action`s and `query`s in `app/main.wasp` and `app/src/risk-engine/operations.ts` to trigger and manage these credential checks.
    *   **2.4. Schema Updates:** Enhance `RiskReport` and `EmploymentRecord` entities in `app/schema.prisma` to include fields for verification status (`isVerified: Boolean`), verification source, and detailed discrepancies.

### 3. Advanced Behavioral Analytics

**Goal:** Identify subtle behavioral red flags that are difficult to spot manually, helping startups build more cohesive and trustworthy teams, particularly in remote settings.

*   **Actionable Steps:**
    *   **3.1. Expand Anomaly Detection Models:** Within `app/src/risk-engine/`, develop more sophisticated AI/ML models for behavioral pattern anomaly detection. This could include analyzing consistency in response times (if email/chat data is consented), identifying unusual work hours, or detecting patterns like frequent disengagement.
    *   **3.2. Communication Platform Integration:** Integrate with consented communication platforms (e.g., Slack, email, calendar data) to gather additional behavioral metrics (with strict privacy controls). This would involve new Wasp `actions` for data ingestion and server-side processing modules.
    *   **3.3. Enhanced RiskReport Details:** Update the `RiskReport` entity in `app/schema.prisma` to capture detailed behavioral flags (e.g., `inconsistentAvailability: Boolean`, `unusualWorkHours: String[]`, `communicationAnomalies: String[]`).

### 4. Comprehensive Risk Scoring & Explainability

**Goal:** Provide a highly transparent and nuanced risk assessment, empowering startup recruiters with clear, justifiable insights to make informed hiring decisions quickly.

*   **Actionable Steps:**
    *   **4.1. Refined Risk Scoring Algorithm:** In `app/src/risk-engine/riskScoring.ts`, implement a more complex algorithm that incorporates multiple factors (employment overlaps, credential mismatches, behavioral flags, GitHub anomalies) with configurable weights.
    *   **4.2. Improved Explanations:** Enhance the `app/src/admin/components/CandidateRiskReportModal.tsx` (or a dedicated page) to offer more detailed, visual explanations of each flag, its data source, and its impact on the overall risk score. Use charts or interactive elements to visualize timelines.
    *   **4.3. Industry Benchmarking:** Develop functionality to compare a candidate's risk profile against anonymized industry benchmarks (if sufficient data is collected with consent). This could involve new analytics queries in `app/src/analytics/operations.ts` and data processing in `app/src/risk-engine/`.
    *   **4.4. Score Calibration:** Implement mechanisms for administrators to calibrate scoring weights and thresholds based on their hiring outcomes and feedback.

### 5. Privacy & Ethics Enhancements

**Goal:** Maintain the highest standards of data privacy and ethical AI use, building trust with candidates and ensuring legal compliance, which is crucial for a reputation-sensitive startup.

*   **Actionable Steps:**
    *   **5.1. Granular Consent Management:** Develop highly granular consent management features within `app/src/client/` (e.g., allowing candidates to selectively share specific data sources like LinkedIn, GitHub, or Calendar) and ensure these preferences are stored in `app/schema.prisma` and respected by backend `data-ingestion` processes.
    *   **5.2. Data Redaction Capabilities:** Implement features for candidates to redact certain parts of their data or reports, with corresponding backend logic in `app/src/server/utils.ts` and `app/src/data-ingestion/`.
    *   **5.3. Enhanced Audit Logging and Reporting:** Strengthen audit logging for all data access, processing, and report generation in `app/src/server/utils.ts` and potentially integrate with `app/src/admin/dashboards/analytics/` for audit trails.
    *   **5.4. Human-in-the-Loop Review:** Introduce explicit human-in-the-loop review steps in the recruiter dashboard workflow within `app/src/admin/`. This could involve a status in `RiskReport` (e.g., 'awaiting_review') and UI for recruiter approval/override.

---

**General Guidance for LLM-Assisted Coding during Implementation:**

*   **Modular Approach:** When asked to implement a step, focus only on the required changes within the specified files/modules.
*   **Wasp-First:** Prioritize defining Wasp entities, operations (`query`, `action`), and tasks (`task`) in `app/main.wasp` and `app/schema.prisma` before writing the actual client/server logic. This ensures the full-stack integration is seamless.
*   **Test-Driven (Implicit):** For each vertical slice, consider how to test its new functionality. Create or extend basic Playwright tests in the `e2e-tests/` directory to validate the integrated feature.
*   **Error Handling:** Implement robust error handling on both client and server sides, providing informative feedback to the user and logging for debugging.
*   **Security:** Always prioritize secure coding practices, especially when handling sensitive candidate data, external API keys, and user authentication.
*   **Refer to `hire-guard-project-map.md`:** This document provides the overall context of the boilerplate structure and should be consulted to ensure new code adheres to existing patterns.
*   **Iterate and Refine:** Each step is a small iteration. Be prepared to refine existing code as new features are added, ensuring backward compatibility and optimal performance.

This comprehensive plan provides a clear roadmap for transforming the `hire-guard` boilerplate into the HireGuard AI application, starting with an achievable MVP tailored to startup needs and progressively adding advanced features.

---

## Phase 2: Roadmap (6-12 Months) - Iterative Enhancements

These phases will build upon the MVP, adding complexity and expanding data sources and AI capabilities. Each item here represents a new vertical slice.

### 1. Enhanced Data Ingestion & Integrations
*   **Actionable Steps:**
    *   Integrate with Calendar/Email APIs (e.g., Google Calendar, Outlook) for event correlation in `app/src/data-ingestion/operations.ts` and new backend modules.
    *   Develop more robust resume parsing (NLP based) in `app/src/risk-engine/`.
    *   Explore ATS integrations (e.g., Greenhouse, Lever) by creating new Wasp operations and server-side connectors.
    *   Update `app/schema.prisma` to store new data types.

### 2. Deeper Credential Verification
*   **Actionable Steps:**
    *   Implement external API calls for university/degree verification.
    *   Develop NLP models within `app/src/risk-engine/` to match claimed credentials against verified data sources.
    *   Add new `action`s and `query`s in `app/main.wasp` to support credential checks.

### 3. Advanced Behavioral Analytics
*   **Actionable Steps:**
    *   Expand `app/src/risk-engine/` with more sophisticated anomaly detection models for behavioral patterns (e.g., chat metrics, interview notes if consented).
    *   Integrate with communication platforms (e.g., Slack, email) for consented data capture.
    *   Enhance `RiskReport` entity in `app/schema.prisma` to capture detailed behavioral flags.

### 4. Comprehensive Risk Scoring & Explainability
*   **Actionable Steps:**
    *   Refine the risk scoring algorithm in `app/src/risk-engine/` to incorporate multiple factors with weighted scores.
    *   Improve the `app/src/admin/components/CandidateRiskReport.tsx` to offer more detailed, visual explanations of flags and their impact on the score.
    *   Implement industry benchmarking and score calibration by storing historical data and performing comparative analysis within `app/src/analytics/` and `app/src/risk-engine/`.

### 5. Privacy & Ethics Enhancements
*   **Actionable Steps:**
    *   Develop granular consent management features for candidates (e.g., selective data sharing).
    *   Implement data redaction capabilities on the frontend and backend.
    *   Enhance audit logging and reporting in `app/src/server/utils.ts`.
    *   Introduce explicit human-in-the-loop review steps in the recruiter dashboard flow within `app/src/admin/`.

---

**General Guidance for LLM-Assisted Coding during Implementation:**

*   **Modular Approach:** When asked to implement a step, focus only on the required changes within the specified files/modules.
*   **Wasp-First:** Prioritize defining Wasp entities, operations, and tasks in `app/main.wasp` and `app/schema.prisma` before writing the actual client/server logic.
*   **Test-Driven (Implicit):** Consider how to test each vertical slice as it's built, using the `e2e-tests/` directory as a guide.
*   **Error Handling:** Include basic error handling in new backend and frontend logic.
*   **Security:** Always prioritize secure coding practices, especially when dealing with user data and external APIs.
*   **Refer to `hire-guard-project-map.md`:** This document provides the overall context of the boilerplate structure.
*   **Iterate and Refine:** Each step is a small iteration. Be prepared to refine existing code as new features are added.

This plan provides a clear roadmap for transforming the `hire-guard` boilerplate into the HireGuard AI application, starting with an achievable MVP and progressively adding advanced features.
