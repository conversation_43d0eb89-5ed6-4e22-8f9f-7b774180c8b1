datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id                        String          @id @default(uuid())
  createdAt                 DateTime        @default(now())

  email                     String?         @unique
  username                  String?         @unique
  isAdmin                   <PERSON>an         @default(false)

  paymentProcessorUserId    String?         @unique
  lemonSqueezyCustomerPortalUrl String?     // You can delete this if you're not using Lemon Squeezy as your payments processor.
  subscriptionStatus        String?         // 'active', 'cancel_at_period_end', 'past_due', 'deleted'
  subscriptionPlan          String?         // 'hobby', 'pro'
  datePaid                  DateTime?
  credits                   Int             @default(3)

  gptResponses              GptResponse[]
  contactFormMessages       ContactFormMessage[]
  tasks                     Task[]
  files                     File[]
}

model GptResponse {
  id                        String          @id @default(uuid())
  createdAt                 DateTime        @default(now())
  updatedAt                 DateTime        @updatedAt

  user                      User            @relation(fields: [userId], references: [id])
  userId                    String

  content                   String
}

model Task {
  id                        String          @id @default(uuid())
  createdAt                 DateTime        @default(now())

  user                      User            @relation(fields: [userId], references: [id])
  userId                    String

  description               String
  time                      String          @default("1")
  isDone                    Boolean         @default(false)
}

model File {
  id                        String          @id @default(uuid())
  createdAt                 DateTime        @default(now())

  user                      User            @relation(fields: [userId], references: [id])
  userId                    String

  name                      String
  type                      String
  key                       String
  uploadUrl                 String
}

model DailyStats {
  id                               Int             @id @default(autoincrement())
  date                             DateTime        @default(now()) @unique

  totalViews                       Int             @default(0)
  prevDayViewsChangePercent        String          @default("0")
  userCount                        Int             @default(0)
  paidUserCount                    Int             @default(0)
  userDelta                        Int             @default(0)
  paidUserDelta                    Int             @default(0)
  totalRevenue                     Float           @default(0)
  totalProfit                      Float           @default(0)

  sources                          PageViewSource[]
}

model PageViewSource {
  @@id([date, name])
  name                     String
  date                     DateTime        @default(now())

  dailyStats               DailyStats?     @relation(fields: [dailyStatsId], references: [id])
  dailyStatsId             Int?

  visitors                 Int
}

model Logs {
  id                       Int             @id @default(autoincrement())
  createdAt                DateTime        @default(now())

  message                  String
  level                    String
}

model ContactFormMessage {
  id                        String          @id @default(uuid())
  createdAt                 DateTime        @default(now())

  user                      User            @relation(fields: [userId], references: [id])
  userId                    String

  content                   String
  isRead                    Boolean         @default(false)
  repliedAt                 DateTime?
}
