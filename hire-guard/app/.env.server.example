# NOTE: you can let <PERSON><PERSON> set up your Postgres DB by running `wasp start db` in a separate terminal window. 
# then, in a new terminal window, run `wasp db migrate-dev` and finally `wasp start`.
# If you use `wasp start db` then you DO NOT need to add a DATABASE_URL env variable here.
# DATABASE_URL=

# For testing, go to https://dashboard.stripe.com/test/apikeys and get a test stripe key that starts with "sk_test_..."
STRIPE_API_KEY=sk_test_...
# After downloading starting the stripe cli (https://stripe.com/docs/stripe-cli) with `stripe listen --forward-to localhost:3001/payments-webhook` it will output your signing secret
STRIPE_WEBHOOK_SECRET=whsec_...
# You can find your Stripe customer portal URL in the Stripe Dashboard under the 'Customer Portal' settings.
STRIPE_CUSTOMER_PORTAL_URL=https://billing.stripe.com/...

# For testing, create a new store in test mode on https://lemonsqueezy.com
LEMONSQUEEZY_API_KEY=eyJ...
# After creating a store, you can find your store id in the store settings https://app.lemonsqueezy.com/settings/stores
LEMONSQUEEZY_STORE_ID=012345
# define your own webhook secret when creating a new webhook on https://app.lemonsqueezy.com/settings/webhooks
LEMONSQUEEZY_WEBHOOK_SECRET=my-webhook-secret

# If using Stripe, go to https://dashboard.stripe.com/test/products and click on + Add Product
# If using Lemon Squeezy, go to https://app.lemonsqueezy.com/products and create new products and variants
PAYMENTS_HOBBY_SUBSCRIPTION_PLAN_ID=012345
PAYMENTS_PRO_SUBSCRIPTION_PLAN_ID=012345
PAYMENTS_CREDITS_10_PLAN_ID=012345

# set this as a comma-separated list of emails you want to give admin privileges to upon registeration
ADMIN_EMAILS=<EMAIL>,<EMAIL>,<EMAIL>

# see our guide for setting up google auth: https://wasp.sh/docs/auth/social-auth/google
GOOGLE_CLIENT_ID=722...
GOOGLE_CLIENT_SECRET=GOC...

# get your sendgrid api key at https://app.sendgrid.com/settings/api_keys
SENDGRID_API_KEY=test...

# (OPTIONAL) get your openai api key at https://platform.openai.com/account
OPENAI_API_KEY=sk-k...

# (OPTIONAL) get your plausible api key at https://plausible.io/login or https://your-plausible-instance.com/login
PLAUSIBLE_API_KEY=gUTgtB...
# You will find your site id in the Plausible dashboard. It will look like 'opensaas.sh'
PLAUSIBLE_SITE_ID=yoursite.com
PLAUSIBLE_BASE_URL=https://plausible.io/api # if you are self-hosting plausible, change this to your plausible instance's base url

# (OPTIONAL) get your google service account key at https://console.cloud.google.com/iam-admin/serviceaccounts
GOOGLE_ANALYTICS_CLIENT_EMAIL=<EMAIL>
# Make sure you convert the private key within the JSON file to base64 first with `echo -n "PRIVATE_KEY" | base64`. see the docs for more info. 
GOOGLE_ANALYTICS_PRIVATE_KEY=LS02...
# You will find your Property ID in the Google Analytics dashboard. It will look like '*********'
GOOGLE_ANALYTICS_PROPERTY_ID=*********

# (OPTIONAL) get your aws s3 credentials at https://console.aws.amazon.com and create a new IAM user with S3 access
AWS_S3_IAM_ACCESS_KEY=ACK...
AWS_S3_IAM_SECRET_KEY=t+33a...
AWS_S3_FILES_BUCKET=your-bucket-name
AWS_S3_REGION=your-region