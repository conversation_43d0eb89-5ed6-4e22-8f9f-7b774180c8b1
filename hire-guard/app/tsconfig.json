// =============================== IMPORTANT =================================
// This file is mainly used for Wasp IDE support.
//
// Wasp will compile your code with slightly different (less strict) compilerOptions.
// You can increase the configuration's strictness (e.g., by adding
// "noUncheckedIndexedAccess": true), but you shouldn't reduce it (e.g., by
// adding "strict": false). Just keep in mind that this will only affect your
// IDE support, not the actual compilation.
//
// Full TypeScript configurability is coming very soon :)
{
  "compilerOptions": {
    "module": "esnext",
    // Needed because this is used as a project reference.
    "composite": true,
    "target": "esnext",
    // We're bundling all code in the end so this is the most appropriate option,
    // it's also important for autocomplete to work properly.
    "moduleResolution": "bundler",
    // JSX support
    "jsx": "preserve",
    "strict": true,
    // Allow default imports.
    "esModuleInterop": true,
    "lib": ["dom", "dom.iterable", "esnext"],
    "skipLibCheck": true,
    "allowJs": true,
    "typeRoots": [
      // This is needed to properly support Vitest testing with jest-dom matchers.
      // Types for jest-dom are not recognized automatically and Typescript complains
      // about missing types e.g. when using `toBeInTheDocument` and other matchers.
      "node_modules/@testing-library",
      // Specifying type roots overrides the default behavior of looking at the
      // node_modules/@types folder so we had to list it explicitly.
      // Source 1: https://www.typescriptlang.org/tsconfig#typeRoots
      // Source 2: https://github.com/testing-library/jest-dom/issues/546#issuecomment-1889884843
      "node_modules/@types"
    ],
    "outDir": ".wasp/out/user"
  },
  "include": ["src"]
}
