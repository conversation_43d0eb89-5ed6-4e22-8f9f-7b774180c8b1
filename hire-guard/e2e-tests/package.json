{"name": "e2e-tests", "version": "1.0.0", "description": "e2e-tests for opensaas.sh", "main": "ci-start-app-and-db.js", "directories": {"test": "tests"}, "scripts": {"ci:e2e:start": "npm run ci:e2e:cleanup-db && node ci-start-app-and-db.js", "ci:e2e:start-db": "cd ../app && wasp start db", "ci:e2e:start-app": "npm run ci:e2e:wait-for-db && cd ../app && wasp db migrate-dev && cd ../e2e-tests && cd ../app && wasp start", "ci:e2e:wait-for-db": "npx wait-port 5432", "_comment-on-ci:e2e:cleanup-db": "NOTE: the name of the DB container, e.g. name=^wasp-dev-db-OpenSaaS-, is generated by wasp and will match the name of the app definition in your `main.wasp` file, e.g. `app OpenSaaS { }`", "ci:e2e:cleanup-db": "(docker container rm $(docker container ls -f name=^wasp-dev-db-OpenSaaS- -q) -f || true) && docker volume rm $(docker volume ls -f name=^wasp-dev-db-OpenSaaS- -q) -f || true", "e2e:playwright": "DEBUG=pw:webserver npx playwright test", "_comment-on-local:e2e:cleanup-stripe": "NOTE: because we are running the stripe webhook listener in the background, we want to make sure we kill the previous processes before starting a new one.", "local:e2e:cleanup-stripe": "PID=$(ps -ef | grep 'stripe listen' | grep -v grep | awk '{print $2}') || true && kill -9 $PID || true", "local:e2e:start-stripe": "stripe listen --forward-to localhost:3001/payments-webhook &", "local:e2e:playwright:ui": "npx playwright test --ui", "local:e2e:start": "npm run local:e2e:cleanup-stripe && npm run local:e2e:start-stripe && npm run local:e2e:playwright:ui && npm run local:e2e:cleanup-stripe"}, "author": "", "license": "ISC", "dependencies": {"@playwright/test": "^1.42.1", "@prisma/client": "5.19.1", "linebyline": "^1.3.0", "prisma": "5.19.1"}, "devDependencies": {"@types/node": "^18.0.0"}}